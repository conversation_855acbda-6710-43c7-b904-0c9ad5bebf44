import IntermediatesManager from "@/intermediates/intermediatesManager.ts";
import { FlexColsCard1x1x1Props } from "@dmp/components/src";
import { rand } from "@vueuse/core";

const intermediatesManager = IntermediatesManager.instance;

const getMockData = (count = 1) =>
  Array(count)
    .fill(0)
    .map(() => ({
      icon: "",
      name: "Iphone",
      count: 50,
      unit: "台",
    }));

intermediatesManager.register(
  "dmp-flex-cols-card1x1x1",
  "/getImportantInfo",
  async (): Promise<FlexColsCard1x1x1Props["data"]> => {
    return getMockData(rand(1, 6));
  },
);
