export default {
  // 布局
  layout: {
    desktop: [
      {
        type: "glassCard",
        title: "数据中心",
        children: [
          {
            type: "dmp-flex-cols-card1x1x1",
            title: "重要信息",
            api: "/getImportantInfo",
          },
          {
            type: "dmp-flex-cols-card1x1x1",
            title: "重要信息",
            api: "/getImportantInfo",
          },
          {
            type: "dmp-flex-cols-card1x1x1",
            title: "重要信息",
            api: "/getImportantInfo",
          },
          {
            type: "dmp-flex-cols-card1x1x1",
            title: "重要信息",
            api: "/getImportantInfo",
          },
          {
            type: "dmp-flex-cols-card1x1x1",
            title: "重要信息",
            api: "/getImportantInfo",
          },
          {
            type: "dmp-flex-cols-card1x1x1",
            title: "重要信息",
            api: "/getImportantInfo",
          },
          {
            type: "dmp-flex-cols-card1x1x1",
            title: "重要信息",
            api: "/getImportantInfo",
          },
          {
            type: "dmp-flex-cols-card1x1x1",
            title: "重要信息",
            api: "/getImportantInfo",
          }
        ],
      },
    ],
    mobile: [
      //... 移动端另一套布局,暂时不需要
    ],
  },
};
