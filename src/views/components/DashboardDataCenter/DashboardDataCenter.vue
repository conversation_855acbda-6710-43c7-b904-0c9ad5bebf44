<script setup>
import DashboardHealthTitleIcon from "./assets/dashboard-health-title-icon.svg";
import { useDashboardDataCenter } from "./composables/useDashboardDataCenter.js";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const { items, salesPerformanceData } = useDashboardDataCenter();
</script>

<template>
  <GlassCard title="数据中心" :icon="DashboardHealthTitleIcon">
    <div class="operation-grid">
      <div class="flex gap-[8px]">
        <div class="flex flex-wrap gap-[8px] w-full shrink-0">
          <DashboardCard008
            :data="salesPerformanceData"
            class="h-[152px] w-[688px]"
            title="销售表现"
          >
            <template #footer>
              <span class="text-[11px] text-[rgba(28,28,28,.40)]"
                >2025/05/20 11:15 {{ t("更新") }}</span
              >
            </template>
          </DashboardCard008>

          <div class="flex gap-[8px]">
            <DashboardCard006
              :data="items"
              class="w-[340px] h-[152px]"
              title="销售表现"
            >
              <template #footer>
                <span class="text-[11px] text-[rgba(28,28,28,.40)]"
                  >2025/05/20 11:15 {{ t("更新") }}</span
                >
              </template>
            </DashboardCard006>

            <DashboardCard006
              :data="items"
              class="w-[340px] h-[152px]"
              title="销售表现"
            >
              <template #footer>
                <span class="text-[11px] text-[rgba(28,28,28,.40)]"
                  >2025/05/20 11:15 {{ t("更新") }}</span
                >
              </template>
            </DashboardCard006>
          </div>
        </div>
      </div>
    </div>
  </GlassCard>
</template>
