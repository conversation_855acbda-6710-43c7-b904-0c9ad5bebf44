<script setup lang="ts">
import { useI18n } from "vue-i18n";
import mockSchema from "@/constants/schema/mockSchema";
import { name2CompMap } from "@/constants/schema/name2CompMap.ts";
import IntermediatesManager from "@/intermediates/intermediatesManager.ts";
import { computed, onMounted } from "vue";
import { useAppStore } from "@/stores/app.js";
// import DmpDataCenterCard1x1 from "@/dataCenterCard1x1/dataCenterCard1x1.vue";
import DmpDataCenterCard1x1 from "../../../../packages/components/src/dataCenterCard1x1/dataCenterCard1x1.vue";

const appStore = useAppStore();
const { t } = useI18n();
const isMobile = computed(() => appStore.isMobile);

const gridTemplates = {
  desktop: {
    col: 6,
  },
  mobile: {
    col: 2,
  },
};

const gridSizes = {
  desktop: {
    row: 166,
    col: 166,
  },
  mobile: {
    row: 166,
    col: 166,
  },
};

const gridTemplate = computed(() =>
  isMobile.value ? gridTemplates.mobile : gridTemplates.desktop,
);
const gridSize = computed(() =>
  isMobile.value ? gridSizes.mobile : gridSizes.desktop,
);
const contentSize = computed(() =>
  isMobile.value
    ? { width: "100%", height: "auto" }
    : {
        width: "100%",
        height: "316px",
      },
);
const dataList = ref([]);

const layoutComps =
  mockSchema.layout.desktop.find((i) => i.title === "数据中心")?.children || [];
const intermediatesManager = IntermediatesManager.instance;

onMounted(async () => {
  dataList.value = await Promise.all(
    layoutComps.map((comp) =>
      intermediatesManager.get(comp.type, comp.api)?.(),
    ),
  );
});

const layoutCompsReady = computed(() =>
  layoutComps.filter((comp, index) => dataList.value[index]),
);


const list = ref([
  {
    lob: "iPhone",
    count: 12,
    unit: "台",
    icon: "",
  },
  {
    lob: "iPad",
    count: 12,
    unit: "台",
    icon: "",
  },
  {
    lob: "Mac",
    count: 12,
    unit: "台",
    icon: "",
  },
  {
    lob: "Watch",
    count: 12,
    unit: "台",
    icon: "",
  },
  {
    lob: "AirPods",
    count: 12,
    unit: "台",
    icon: "",
  },
]);
</script>

<template>
  <dmp-glass-card
    :title="t('数据中心')"
    :grid-template
    :grid-size
    :content-size
    :disable-scrolling="isMobile"
  >
    <DmpDataCenterCard1x1 :title="t('销售表现')" :updateTime="'2025/05/20 11:15'" :list="list" />
    <!-- <component
      v-for="(comp, index) of layoutCompsReady"
      :key="comp.title + comp.type"
      :title="comp.title"
      :is="name2CompMap[comp.type]"
      :data="dataList[index]"
    /> -->
  </dmp-glass-card>
</template>

<style lang="scss" scoped>
:deep(.dmp-flex-card) {
  padding-inline: 8px;
  padding-bottom: 8px;

  .title {
    padding-inline: 8px;
  }

  .content {
    flex: 1;

    ::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
