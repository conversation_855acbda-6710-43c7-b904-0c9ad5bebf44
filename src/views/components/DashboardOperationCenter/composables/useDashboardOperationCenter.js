/*
 * @Date: 2025-07-04 15:12:48
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-04 15:19:10
 * @FilePath: /MyBusinessV2/src/views/components/DashboardOperationCenter/composables/useDashboardOperationCenter.js
 */
/*
| ---------------------------- | ---------------------------- | ------------------------------------------------------------ |
| 运营异常                     | **`DashboardCard002`**      | 中等尺寸网格卡；显示 1‑2 关键数字，可选右上角徽章/图标。     |
| 备货计划                     | **`DashboardCard002`**      | 中等尺寸网格卡；显示 1‑2 关键数字，可选右上角徽章/图标。     |
| 分货计划                     | **`DashboardCard002`**      | 中等尺寸网格卡；显示 1‑2 关键数字，可选右上角徽章/图标。     |
| 运营评分                     | **`DashboardCard002`**      | 中等尺寸网格卡；显示 1‑2 关键数字，可选右上角徽章/图标。     |
| 提升项目                     | **`DashboardCard002`**      | 中等尺寸网格卡；显示 1‑2 关键数字，可选右上角徽章/图标。     |
*/

/**
| ---------------------------- | ---------------------------- | ------------------------------------------------------------ |
| 我的申报                     | **`DashboardCard003`**      | 与 `DashboardCard002` 尺寸相同，但强调「动作」；支持点击后跳详情或弹窗。  |
| 活动管理                     | **`DashboardCard003`**      | 与 `DashboardCard002` 尺寸相同，但强调「动作」；支持点击后跳详情或弹窗。  |
*/

/**
| ---------------------------- | ---------------------------- | ------------------------------------------------------------ |
| 项目表现                     | **`DashboardCard004`**      | 仍沿用中等卡尺寸，但专门展示项目达成率、进度条等。  
*/

/*
| ---------------------------- | ---------------------------- | ------------------------------------------------------------ |
| 提升项目                     | **`DashboardCard002`**      | 中等尺寸网格卡；显示 1‑2 关键数字，可选右上角徽章/图标。     |
*/
export const useDashboardOperationCenter = () => {
  const items = ref([
    {
      lob: "iPhone",
      number: 100,
    },
    {
      lob: "iPad",
      number: 100,
    },
    {
      lob: "Mac",
      number: 100,
    },
    {
      lob: "Watch",
      number: 100,
    },
    {
      lob: "HomePod",
      number: 100,
    },
  ]);

  const tabs = [
    {
      label: "iPhone",
      value: "apple",
    },
    {
      label: "iPad",
      value: "third",
    },
    {
      label: "Watch",
      value: "Watch",
    },
    {
      label: "配件",
      value: "配件",
    },
  ];

  return {
    items,
    tabs,
  };
};
