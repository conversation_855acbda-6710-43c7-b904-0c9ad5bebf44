<script setup>
import DashboardHealthTitleIcon from "./assets/dashboard-health-title-icon.svg";
import { useDashboardOperationCenter } from "./composables/useDashboardOperationCenter.js";
import { useAppStore } from "@/stores/app.js";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const { tabs, items } = useDashboardOperationCenter();

const appStore = useAppStore();

const activeTab = ref(tabs[0].value);

const handleTabChange = (value) => {
  activeTab.value = value;
};
</script>

<template>
  <GlassCard :title="t('运营中心')" :icon="DashboardHealthTitleIcon">
    <div
      :class="appStore.isMobile ? 'flex flex-col gap-[8px]' : 'flex gap-[8px]'"
    >
      <CardWithTitle
        :title="t('配件排行榜')"
        :class="appStore.isMobile ? 'w-full' : 'shrink-0 w-[340px]'"
      >
        <div class="flex flex-col gap-[8px]">
          <TabSwitcher
            :tabs="tabs"
            equalWidth
            @change="handleTabChange"
          ></TabSwitcher>

          <div v-if="activeTab === 'apple'">
            <ItemWithArrow class="h-[72px]">
              <div class="flex items-center gap-[12px]">
                <div
                  class="w-[56px] h-[56px] rounded-[8px] shrink-0 bg-[#fff]"
                ></div>
                <div class="flex flex-col gap-[2px]">
                  <div class="text-[13px] text-[#6E6E73]">
                    20W USB‑C 电源适配器
                  </div>
                  <div
                    class="text-[12px] text-[#1C1C1E] flex items-end gap-[2px]"
                  >
                    <span
                      class="text-[16px] font-medium leading-[16px] text-[#1C1C1E]"
                      >RMB 149</span
                    >
                    <span class="text-[10px] text-[#6E6E73] leading-[13px]"
                      >官网价</span
                    >
                  </div>
                </div>
              </div>
            </ItemWithArrow>
            <ItemWithArrow class="h-[72px]">
              <div class="flex items-center gap-[12px]">
                <div
                  class="w-[56px] h-[56px] rounded-[8px] shrink-0 bg-[#fff]"
                ></div>
                <div class="flex flex-col gap-[2px]">
                  <div class="text-[13px] text-[#6E6E73]">
                    20W USB‑C 电源适配器
                  </div>
                  <div
                    class="text-[12px] text-[#1C1C1E] flex items-end gap-[2px]"
                  >
                    <span
                      class="text-[16px] font-medium leading-[16px] text-[#1C1C1E]"
                      >RMB 149</span
                    >
                    <span class="text-[10px] text-[#6E6E73] leading-[13px]"
                      >官网价</span
                    >
                  </div>
                </div>
              </div>
            </ItemWithArrow>
            <ItemWithArrow class="h-[72px]">
              <div class="flex items-center gap-[12px]">
                <div
                  class="w-[56px] h-[56px] rounded-[8px] shrink-0 bg-[#fff]"
                ></div>
                <div class="flex flex-col gap-[2px]">
                  <div class="text-[13px] text-[#6E6E73]">
                    20W USB‑C 电源适配器
                  </div>
                  <div
                    class="text-[12px] text-[#1C1C1E] flex items-end gap-[2px]"
                  >
                    <span
                      class="text-[16px] font-medium leading-[16px] text-[#1C1C1E]"
                      >RMB 149</span
                    >
                    <span class="text-[10px] text-[#6E6E73] leading-[13px]"
                      >官网价</span
                    >
                  </div>
                </div>
              </div>
            </ItemWithArrow>
          </div>

          <div v-if="activeTab === 'third'">
            <ItemWithArrow class="h-[72px]">
              <div class="flex items-center gap-[12px]">
                <div
                  class="w-[56px] h-[56px] rounded-[8px] shrink-0 bg-[#fff]"
                ></div>
                <div class="flex flex-col gap-[2px]">
                  <div class="text-[13px] text-[#6E6E73]">
                    20W USB‑C 电源适配器 第三方配件
                  </div>
                  <div
                    class="text-[12px] text-[#1C1C1E] flex items-end gap-[2px]"
                  >
                    <span
                      class="text-[16px] font-medium leading-[16px] text-[#1C1C1E]"
                      >RMB 149</span
                    >
                    <span class="text-[10px] text-[#6E6E73] leading-[13px]"
                      >官网价</span
                    >
                  </div>
                </div>
              </div>
            </ItemWithArrow>
            <ItemWithArrow class="h-[72px]">
              <div class="flex items-center gap-[12px]">
                <div
                  class="w-[56px] h-[56px] rounded-[8px] shrink-0 bg-[#fff]"
                ></div>
                <div class="flex flex-col gap-[2px]">
                  <div class="text-[13px] text-[#6E6E73]">
                    20W USB‑C 电源适配器 第三方配件
                  </div>
                  <div
                    class="text-[12px] text-[#1C1C1E] flex items-end gap-[2px]"
                  >
                    <span
                      class="text-[16px] font-medium leading-[16px] text-[#1C1C1E]"
                      >RMB 149</span
                    >
                    <span class="text-[10px] text-[#6E6E73] leading-[13px]"
                      >官网价</span
                    >
                  </div>
                </div>
              </div>
            </ItemWithArrow>
            <ItemWithArrow class="h-[72px]">
              <div class="flex items-center gap-[12px]">
                <div
                  class="w-[56px] h-[56px] rounded-[8px] shrink-0 bg-[#fff]"
                ></div>
                <div class="flex flex-col gap-[2px]">
                  <div class="text-[13px] text-[#6E6E73]">
                    20W USB‑C 电源适配器 第三方配件
                  </div>
                  <div
                    class="text-[12px] text-[#1C1C1E] flex items-end gap-[2px]"
                  >
                    <span
                      class="text-[16px] font-medium leading-[16px] text-[#1C1C1E]"
                      >RMB 149</span
                    >
                    <span class="text-[10px] text-[#6E6E73] leading-[13px]"
                      >官网价</span
                    >
                  </div>
                </div>
              </div>
            </ItemWithArrow>
          </div>
        </div>
      </CardWithTitle>

      <DashboardCard006
        :data="items"
        :class="appStore.isMobile ? 'w-full' : 'w-[340px] h-[152px]'"
        title="产品销售排名"
      ></DashboardCard006>
    </div>
  </GlassCard>
</template>
