<script setup>
import { useDashboardHealth } from "./composables/useDashboardHealth";
import DashboardHealthTitleIcon from "./assets/dashboard-health-title-icon.svg";
import { useAppStore } from "@/stores/app.js";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const { items, updateDotPositions } = useDashboardHealth();
const appStore = useAppStore();

const isMobile = computed(() => appStore.isMobile);

const healthTips = ["您的系统出现多个预警信号，建议立即检查！"];

const healthListRef = useTemplateRef("healthListRef");

const windowSizeObserver = new ResizeObserver((entries) => {
  entries.forEach(() => {
    updateDotPositions();
  });
});

onMounted(() => {
  updateDotPositions();
  windowSizeObserver.observe(healthListRef.value);
});

onUnmounted(() => {
  windowSizeObserver.disconnect();
});

watch(
  items,
  () => {
    updateDotPositions();
  },
  { deep: true, flush: "post" }, // 使用 post 刷新，确保 DOM 更新完成后再计算圆点位置
);
</script>

<template>
  <!-- todo: 小圆点还需要优化, 浏览器缩放的同时，小圆点位置移动会被看见-->
  <GlassCard title="健康诊断" :icon="DashboardHealthTitleIcon">
    <div class="flex flex-col">
      <div class="flex flex-col gap-[8px]">
        <div
          :class="[
            'health-container card',
            isMobile ? 'h-[120px]' : 'h-[152px]',
          ]"
        >
          <div class="health-content">
            <div class="progress-section">
              <ArcProgress
                :progress="96"
                label="健康度"
                open-direction="top"
                :size="isMobile ? 70 : 90"
                :stroke-width="isMobile ? 6 : 8"
              />
              <div
                :class="[
                  'font-medium leading-[16px] text-center',
                  isMobile ? 'text-[10px]' : 'text-[11px]',
                ]"
              >
                <p v-for="(tip, index) in healthTips" :key="index">
                  {{ tip }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="card py-[8px] h-[312px]" ref="healthListRef">
          <div class="flex flex-col">
            <div
              class="flex flex-col py-[10px]"
              v-for="(item, index) in items"
              :key="item.label"
              :class="{
                'border-b border-[rgba(0,0,0,0.04)]':
                  index !== items.length - 1,
              }"
            >
              <div class="flex justify-between">
                <div
                  :class="[
                    'font-normal leading-[16px]',
                    isMobile ? 'text-[12px]' : 'text-[13px]',
                  ]"
                >
                  {{ t(item.label) }}
                </div>

                <div class="progress-bar" :data-percentage="item.value">
                  <el-progress
                    :percentage="item.value"
                    :color="item.color"
                    :stroke-width="4"
                    class="custom-progress-bar"
                  />
                  <div class="custom-dot"></div>
                </div>

                <div
                  :class="[
                    'font-normal leading-[16px]',
                    isMobile ? 'text-[12px]' : 'text-[13px]',
                  ]"
                >
                  暂无异常
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </GlassCard>
</template>

<style scoped>
.health-container {
  padding: 16px;
}

.health-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.health-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.health-score-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.score-number {
  font-size: 28px;
  font-weight: bold;
  color: #2ebb72;
  line-height: 1;
}

.score-label {
  font-size: 12px;
  color: #666;
  line-height: 1;
}

.health-status {
  font-size: 14px;
  font-weight: 500;
  color: #2ebb72;
}

.health-tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tip-card {
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.4;
  border-left: 3px solid;
}

.tip-card.warning {
  background-color: #fff3e0;
  border-left-color: #ff9800;
  color: #e65100;
}

.tip-card.success {
  background-color: #e8f5e8;
  border-left-color: #4caf50;
  color: #2e7d32;
}

.progress-bar {
  position: relative;
  height: 14px;
  overflow: visible;
  width: 42%;
}

:deep(.progress-bar .el-progress-bar__outer) {
  background: #dadee0;
}

:deep(.progress-bar .el-progress) {
  width: 100%;
}

/* 移动端优化进度条宽度 */
@media (max-width: 768px) {
  .progress-bar {
    width: 46%;
  }

  :deep(.progress-bar .el-progress) {
    width: 100%;
  }
}

.custom-dot {
  position: absolute;
  top: 50%;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1.5px solid #ffffff;
  background: #727376;
  transform: translate(-50%, -50%);
  z-index: 10;
  transition: left 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}
</style>
