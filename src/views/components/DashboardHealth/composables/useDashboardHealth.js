export const useDashboardHealth = () => {
  const items = ref([
    {
      label: "库存预警",
      value: 55,
      color: "rgba(28, 28, 28, 0.24)",
    },
    {
      label: "7天激活率",
      value: 88,
      color: "rgba(28, 28, 28, 0.24)",
    },
    {
      label: "同城激活率",
      value: 77,
      color: "rgba(28, 28, 28, 0.24)",
    },
    {
      label: "销售预警",
      value: 66,
      color: "rgba(28, 28, 28, 0.24)",
    },
    {
      label: "扫码预警",
      value: 12,
      color: "rgba(28, 28, 28, 0.24)",
    },
    {
      label: "门店缺货",
      value: 34,
      color: "rgba(28, 28, 28, 0.24)",
    },
    {
      label: "在仓库龄",
      value: 22,
      color: "rgba(28, 28, 28, 0.24)",
    },
    {
      label: "库存提醒",
      value: 86,
      color: "rgba(28, 28, 28, 0.24)",
    },
  ]);

  // 计算圆点位置
  function updateDotPositions() {
    nextTick(() => {
      const progressBars = document.querySelectorAll(".progress-bar");
      progressBars.forEach((progressBar) => {
        const progressOuter = progressBar.querySelector(
          ".el-progress-bar__outer",
        );
        const progressInner = progressBar.querySelector(
          ".el-progress-bar__inner",
        );
        const dot = progressBar.querySelector(".custom-dot");

        if (progressOuter && progressInner && dot) {
          // 获取外层容器的宽度
          const outerWidth = progressOuter.offsetWidth;
          // 从 data-percentage 属性获取实际的进度百分比
          const percentage = parseInt(progressBar.dataset.percentage) || 0;
          // 计算圆点位置
          const dotPosition = (outerWidth * percentage) / 100;
          dot.style.left = `${dotPosition}px`;
        }
      });
    });
  }

  return {
    items,
    updateDotPositions,
  };
};
