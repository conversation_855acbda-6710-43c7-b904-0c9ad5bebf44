<script setup lang="ts">
import { useI18n } from "vue-i18n";
import mockSchema from "@/constants/schema/mockSchema";
import { name2CompMap } from "@/constants/schema/name2CompMap.ts";
import IntermediatesManager from "@/intermediates/intermediatesManager.ts";
import { computed, onMounted } from "vue";
import { useAppStore } from "@/stores/app.js";

const appStore = useAppStore();
const { t } = useI18n();
const isMobile = computed(() => appStore.isMobile);

const gridTemplates = {
  desktop: {
    col: 4,
  },
  mobile: {
    col: 2,
  },
};

const gridSizes = {
  desktop: {
    row: 152,
    col: 152,
  },
  mobile: {
    row: 152,
    col: 152,
  },
};

const gridTemplate = computed(() =>
  isMobile.value ? gridTemplates.mobile : gridTemplates.desktop,
);
const gridSize = computed(() =>
  isMobile.value ? gridSizes.mobile : gridSizes.desktop,
);
const contentSize = computed(() =>
  isMobile.value
    ? { width: "100%", height: "auto" }
    : {
        width: "100%",
        height: "316px",
      },
);
const dataList = ref([]);

const layoutComps =
  mockSchema.layout.desktop.find((i) => i.title === "数据中心")?.children || [];
const intermediatesManager = IntermediatesManager.instance;

onMounted(async () => {
  dataList.value = await Promise.all(
    layoutComps.map((comp) =>
      intermediatesManager.get(comp.type, comp.api)?.(),
    ),
  );
});

const layoutCompsReady = computed(() =>
  layoutComps.filter((comp, index) => dataList.value[index]),
);
</script>

<template>
  <dmp-glass-card
    :title="t('数据中心')"
    :grid-template
    :grid-size
    :content-size
    :disable-scrolling="isMobile"
  >
    <component
      v-for="(comp, index) of layoutCompsReady"
      :key="comp.title + comp.type"
      :title="comp.title"
      :is="name2CompMap[comp.type]"
      :data="dataList[index]"
    />
  </dmp-glass-card>
</template>
