<script setup>
import { useUserStore } from "@/stores";
import { useAppStore } from "@/stores/app.js";
import ResellerLevelIcon from "@/assets/icons/reseller-level-icon.svg";
import DashboardHealth from "@/views/components/DashboardHealth/DashboardHealth.vue";
import DashboardOperationCenter from "@/views/components/DashboardOperationCenter/DashboardOperationCenter.vue";
import DashboardMessageCenter from "@/views/components/DashboardMessageCenter/DashboardMessageCenter.vue";
// import DashboardDataCenter from "@/views/components/DashboardDataCenter/DashboardDataCenter.vue";
import DashboardDataCenter2 from "@/views/components/dashboardDataCenter2/dashboardDataCenter.vue";
import DashboardDataCenter from "@/views/components/DashboardDataCenter3/DashboardDataCenter.vue";
import { loadLanguageAsync } from "@/i18n";
import { i18n } from "@/i18n";

const userStore = useUserStore();
const appStore = useAppStore();

// 获取响应式状态
const isMobile = computed(() => appStore.isMobile);

// 语言选择
const language = ref(i18n.global.locale);

const changeLanguage = (lang) => {
  loadLanguageAsync(lang);
};

onMounted(() => {
  if (userStore.isAuthenticated) {
    notify.success("测试notify组件功能是否正常");
  }
});
</script>

<template>
  <div>
    <el-select v-model="language" @change="changeLanguage" style="width: 240px">
      <el-option label="中文" value="zh" />
      <el-option label="英文" value="en" />
      <el-option label="繁体" value="hk" />
      <el-option label="简体" value="tw" />
    </el-select>
  </div>
  <div class="space-y-[24px]">
    <!-- Header -->
    <div
      :class="[
        'flex items-center gap-[16px]',
        // 移动端垂直布局，桌面端水平布局
        isMobile ? 'flex-col items-start' : 'justify-between',
      ]"
    >
      <div class="flex gap-[8px] items-center">
        <h1 class="text-[24px] text-black font-bold">
          {{ userStore.resellerInfo?.reseller_name || "-" }}
        </h1>
        <div
          class="flex items-center gap-[2px] bg-[rgba(255,255,255,.32)] rounded-[28px] px-[6px] py-[2px]"
        >
          <ResellerLevelIcon class="w-[12px] h-[12px]" />
          <span class="text-[12px] text-[#000000]">
            {{ userStore.resellerInfo?.reseller_level || "-" }}
          </span>
        </div>
      </div>

      <!-- <div
        :class="[
          'flex h-[56px] dashboard-score-wrapper flex-col items-start gap-[2px] bg-[rgba(255,255,255,.32)] rounded-[28px] px-[16px] py-[9px] pl-[64px]',
          // 移动端全宽，桌面端固定宽度
          isMobile ? 'w-full' : 'w-[284px]',
        ]"
      >
        <div class="text-[14px] text-[#1C1C1E] leading-[20px] font-medium">
          本周积分 425
        </div>

        <div class="flex items-center gap-[4px] text-[12px] leading-[16px]">
          <div class="flex gap-[4px]">
            <span class="text-[rgba(28,28,30,.64)]">全国排名前</span>
            <span class="text-[#1C1C1E] font-medium">10%</span>
          </div>

          <div
            class="w-[2px] h-[2px] bg-[rgba(28,28,30,.64)] rounded-full"
          ></div>

          <div class="flex gap-[4px]">
            <span class="text-[rgba(28,28,30,.64)]">总积分</span>
            <span class="text-[#1C1C1E] font-medium">10000</span>
          </div>
        </div>
      </div> -->
    </div>

    <!-- Main Content -->
    <div
      :class="[
        'flex gap-[16px]',
        // 移动端垂直布局，桌面端水平布局
        isMobile ? 'flex-col' : 'flex-row',
      ]"
    >
      <!-- 健康诊断组件 -->
      <div
        :class="[
          'flex flex-col gap-[16px]',
          // 移动端全宽，桌面端固定宽度
          isMobile ? 'w-full' : 'w-[332px] shrink-0',
        ]"
      >
        <DashboardHealth
          class="w-full h-[544px]"
          v-log.visible="{ event_name: 'dashboard_health' }"
          v-log.stay="{ event_name: 'dashboard_health' }"
        />
      </div>

      <!-- 消息中心和运营中心 -->
      <div class="flex flex-col gap-[16px] flex-1 overflow-hidden">
        <DashboardMessageCenter
          :class="[
            'w-full',
            // 移动端调整高度，桌面端保持原高度
            isMobile ? 'h-auto min-h-[120px]' : 'h-[144px]',
          ]"
        />

        <!-- 数据中心 -->
        <dashboard-data-center2
          :class="[
            isMobile ? 'w-full' : 'w-[664px]',
            isMobile ? 'h-auto' : 'h-[384px]',
          ]"
        />
      </div>
    </div>

    <DashboardDataCenter
      :class="[
        'w-full',
        // 移动端调整高度，桌面端保持原高度
        isMobile ? 'h-auto min-h-[300px]' : 'h-[384px]',
      ]"
    />

    <DashboardOperationCenter
      :class="[
        'w-full',
        // 移动端调整高度，桌面端保持原高度
        isMobile ? 'h-auto min-h-[300px]' : 'h-[384px]',
      ]"
    />
  </div>
</template>

<style scoped>
/* 确保卡片在小屏幕上的响应式行为 */
@media (max-width: 1024px) {
  .col-span-12 {
    grid-column: span 12;
  }
}

/* 卡片悬停效果 */
.bg-white:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

.dashboard-score-wrapper {
  background: url(../assets/dashboard/dashboard-score-bg.png)
    rgba(255, 255, 255, 0.32);
  background-size: cover;
}
</style>
