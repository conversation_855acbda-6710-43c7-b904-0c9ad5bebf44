<script setup>
import ArrowRightIcon from "@/assets/icons/arrow-right.svg";

defineProps({
  title: {
    type: String,
    required: true,
  },
});
</script>

<template>
  <div class="card flex flex-col gap-[8px]">
    <div class="title flex items-center">
      <span class="text-[13px] text-[#1c1c1e] font-medium">{{ title }}</span>
      <ArrowRightIcon class="w-[16px] h-[16px]" />
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped></style>
