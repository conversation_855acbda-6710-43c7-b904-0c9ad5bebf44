<template>
  <div class="arc-progress-container">
    <svg
      :width="size"
      :height="size"
      viewBox="0 0 100 100"
      class="arc-progress-svg"
    >
      <!-- 定义渐变 -->
      <defs>
        <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color: #2ebb72; stop-opacity: 1" />
          <stop offset="100%" style="stop-color: #9de76a; stop-opacity: 1" />
        </linearGradient>
      </defs>

      <!-- 背景弧线 -->
      <path
        :d="backgroundPath"
        fill="none"
        :stroke="backgroundColor"
        :stroke-width="strokeWidth"
        stroke-linecap="round"
      />

      <!-- 进度弧线 -->
      <path
        :d="progressPath"
        fill="none"
        stroke="url(#progressGradient)"
        :stroke-width="strokeWidth"
        stroke-linecap="round"
        :stroke-dasharray="progressDashArray"
        :stroke-dashoffset="progressDashOffset"
      />

      <!-- 圆点 -->
      <circle
        v-if="progress > 0"
        :cx="dotPosition.x"
        :cy="dotPosition.y"
        :r="dotSize / 2"
        fill="#9DE76A"
        stroke="#FFFFFF"
        stroke-width="1"
      />
    </svg>

    <!-- 中间内容 -->
    <div class="arc-progress-content">
      <div class="progress-value">{{ Math.round(progress) }}</div>
      <div class="progress-label">{{ label }}</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";

// 定义props
const props = defineProps({
  progress: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100,
  },
  size: {
    type: Number,
    default: 90,
  },
  label: {
    type: String,
    default: "健康度",
  },
  strokeWidth: {
    type: Number,
    default: 6,
  },
  backgroundColor: {
    type: String,
    default: "rgba(28,28,30,0.06)",
  },
  dotSize: {
    type: Number,
    default: 8,
  },
  // 新增开口方向配置
  openDirection: {
    type: String,
    default: "bottom",
    validator: (value) => ["top", "bottom", "left", "right"].includes(value),
  },
  // 弧线角度范围（度数）
  arcAngle: {
    type: Number,
    default: 270,
    validator: (value) => value > 0 && value <= 360,
  },
});

// 计算安全边距，确保圆点不会被切掉
const safeMargin = computed(
  () => Math.max(props.strokeWidth / 2, props.dotSize / 2) + 2,
);

// 弧线的半径，考虑安全边距
const radius = computed(() => (100 - safeMargin.value * 2) / 2);

// 圆心坐标，考虑安全边距
const centerX = computed(() => 50);
const centerY = computed(() => 50);

// 根据开口方向计算起始角度和结束角度
const { startAngle, endAngle } = computed(() => {
  const halfArc = props.arcAngle / 2;
  let centerAngle;

  switch (props.openDirection) {
    case "top":
      centerAngle = -90; // 顶部开口，中心在上方
      break;
    case "bottom":
      centerAngle = 90; // 底部开口，中心在下方
      break;
    case "left":
      centerAngle = 180; // 左侧开口，中心在左方
      break;
    case "right":
      centerAngle = 0; // 右侧开口，中心在右方
      break;
    default:
      centerAngle = 90;
  }

  return {
    startAngle: centerAngle - halfArc,
    endAngle: centerAngle + halfArc,
  };
}).value;

// 创建弧线路径的函数
const createArcPath = (startAngle, endAngle) => {
  const startRadian = (startAngle * Math.PI) / 180;
  const endRadian = (endAngle * Math.PI) / 180;

  const startX = centerX.value + radius.value * Math.cos(startRadian);
  const startY = centerY.value + radius.value * Math.sin(startRadian);
  const endX = centerX.value + radius.value * Math.cos(endRadian);
  const endY = centerY.value + radius.value * Math.sin(endRadian);

  const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;

  return `M ${startX} ${startY} A ${radius.value} ${radius.value} 0 ${largeArcFlag} 1 ${endX} ${endY}`;
};

// 背景弧线路径
const backgroundPath = computed(() => createArcPath(startAngle, endAngle));

// 进度弧线路径
const progressPath = computed(() => createArcPath(startAngle, endAngle));

// 进度弧线的长度
const arcLength = computed(() => {
  const angle = ((endAngle - startAngle) * Math.PI) / 180;
  return radius.value * angle;
});

// 进度dasharray
const progressDashArray = computed(
  () => `${arcLength.value} ${arcLength.value}`,
);

// 进度dashoffset
const progressDashOffset = computed(() => {
  const progressLength = (arcLength.value * props.progress) / 100;
  return arcLength.value - progressLength;
});

// 圆点位置
const dotPosition = computed(() => {
  if (props.progress <= 0) return { x: 0, y: 0 };

  const angle = startAngle + (endAngle - startAngle) * (props.progress / 100);
  const radian = (angle * Math.PI) / 180;

  return {
    x: centerX.value + radius.value * Math.cos(radian),
    y: centerY.value + radius.value * Math.sin(radian),
  };
});
</script>

<style scoped>
.arc-progress-container {
  position: relative;
  display: inline-block;
}

.arc-progress-svg {
  display: block;
}

.arc-progress-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.progress-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 2px;
}

.progress-label {
  font-size: 12px;
  color: #666;
  line-height: 1;
}
</style>
