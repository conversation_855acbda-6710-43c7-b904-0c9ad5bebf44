<script setup lang="ts">
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useAppStore } from "@/stores/app.js";
import { useUserStore } from "@/stores/user.js";
import { MenuItem } from "@dmp/components/src/index.js";
import { menuItems as routeMenuItems } from "@/config/menu.js";

const route = useRoute();
const router = useRouter();
const appStore = useAppStore();
const userStore = useUserStore();

// 从 store 中获取状态
const shouldShowSidebar = computed(() => appStore.shouldShowSidebar);
const isMobile = computed(() => appStore.isMobile);

// 菜单项配置 - 从路由文件导入并根据权限过滤
const filteredMenus = userStore.userRtm
  ? userStore.filterMenuItems(routeMenuItems)
  : [];

function gotoPage(menuItem: MenuItem) {
  if (menuItem.path && menuItem.path !== route.path) {
    router.push(menuItem.path);
    // 移动端点击子菜单后关闭菜单
    if (isMobile.value) {
      appStore.closeMobileMenu();
    }
  }
}
</script>

<template>
  <dmp-sidebar
    :initPath="route.path"
    :menuItems="filteredMenus"
    :show="shouldShowSidebar"
    :isMobile="isMobile"
    @select="gotoPage"
  />
</template>
