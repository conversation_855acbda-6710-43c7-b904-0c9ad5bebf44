<script setup>
import { computed } from "vue";
import { useAppStore } from "@/stores/app.js";
import ToggleIcon from "@/assets/icons/toggle-sidebar-icon.svg";

const appStore = useAppStore();

// 从 store 中获取状态
const shouldShowSidebar = computed(() => appStore.shouldShowSidebar);
const isMobile = computed(() => appStore.isMobile);

// 切换侧边栏展开状态
function toggleSidebar() {
  appStore.toggleExpanded();
}
</script>

<template>
  <ToggleIcon
    :class="[
      'w-[40px] h-[40px] fixed top-[12px] p-0 flex items-center justify-center cursor-pointer transition-transform duration-300 z-50',
      // 移动端：始终显示在左上角固定位置
      // 桌面端：根据侧边栏状态调整位置
      isMobile
        ? 'translate-x-[12px]'
        : shouldShowSidebar
          ? 'translate-x-[219px]'
          : 'translate-x-[12px]',
    ]"
    @click="toggleSidebar"
  />
</template>

<style scoped></style>
