<script setup>
import { RouterView, useRoute } from "vue-router";
import { computed, onMounted } from "vue";
import Sidebar from "./components/layout/Sidebar.vue";
import SidebarToggle from "@/components/layout/SidebarToggle.vue";
import { useAppStore } from "@/stores/app.js";

const route = useRoute();
const appStore = useAppStore();

const shouldShowSidebar = computed(() => appStore.shouldShowSidebar);
const isMobile = computed(() => appStore.isMobile);

// 根据路由 meta 判断是否需要布局
const needsLayout = computed(() => {
  return route.meta?.requiresLayout !== false;
});

onMounted(async () => {
  appStore.initializeResponsiveState(); // 初始化响应式状态
  console.log("📱 响应式状态初始化完成");
});

// 关闭移动端菜单
function closeMobileMenu() {
  if (isMobile.value) {
    appStore.closeMobileMenu();
  }
}
</script>

<template>
  <div id="app" class="h-screen overflow-hidden relative">
    <!-- 需要布局的页面 -->
    <template v-if="needsLayout">
      <!-- 移动端遮罩层 -->
      <div
        v-if="isMobile && shouldShowSidebar"
        class="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
        @click="closeMobileMenu"
      ></div>

      <!-- 左侧边栏 -->
      <Sidebar />

      <!-- 侧边栏切换按钮 -->
      <SidebarToggle />

      <!-- 主内容区域 -->
      <main
        :class="[
          'h-full overflow-y-auto p-6 pt-[64px] transition-all duration-300 overscroll-y-contain',
          isMobile ? 'ml-0' : shouldShowSidebar ? 'ml-[200px]' : 'ml-0',
        ]"
      >
        <Suspense>
          <div class="max-w-[1068px] mx-auto">
            <RouterView />
          </div>
          <template #fallback>
            <div class="flex items-center justify-center h-64">
              <div
                class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"
              ></div>
            </div>
          </template>
        </Suspense>
      </main>
    </template>

    <!-- 不需要布局的页面（全屏显示） -->
    <template v-else>
      <Suspense>
        <RouterView />
        <template #fallback>
          <div class="flex items-center justify-center h-screen">
            <div
              class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"
            ></div>
          </div>
        </template>
      </Suspense>
    </template>
  </div>
</template>

<style>
#app {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overscroll-behavior-y: contain;
  background: linear-gradient(180deg, #b9c2ca 0%, #e7ebed 100%);
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

main::-webkit-scrollbar {
  width: 6px;
}

main::-webkit-scrollbar-track {
  background: transparent;
}

main::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

main::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
