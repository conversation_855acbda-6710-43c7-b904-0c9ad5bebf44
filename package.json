{"name": "mybizv2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "buildDependencies": "pnpm --filter=@dmp/* run build", "preview": "vite preview"}, "dependencies": {"@dmp/components": "workspace:*", "@dmp/resolver": "workspace:*", "@vueuse/core": "^10.7.2", "axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.4.4", "pinia": "^2.1.7", "uuid": "^11.1.0", "vue": "^3.5.17", "vue-echarts": "^7.0.3", "vue-i18n": "^11.1.8", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.11.10", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "less": "^4.3.0", "postcss": "^8.4.33", "prettier": "^3.2.4", "tailwindcss": "^3.4.1", "typescript": "~5.3.0", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.8.0", "vite": "^5.0.11", "vite-plugin-dts": "^4.5.4", "vite-plugin-eslint": "^1.8.1", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.0.29"}}