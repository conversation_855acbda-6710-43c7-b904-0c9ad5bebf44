<script setup lang="ts">
import { Icon } from "@/icon/icon";
import { FlexCardProps } from "@/flexCard/props";

defineOptions({ name: "DmpFlexCard" })
const {
  title,
  showArrow = true,
  gridSpan = {
    row: 1,
    col: 1
  }
} = defineProps<FlexCardProps>();
</script>

<template>
  <div class="dmp-flex-card flex flex-col gap-[8px]" :style="{ gridRow: `span ${gridSpan.row}`, gridColumn: `span ${gridSpan.col}` }">
    <div class="title flex items-center">
      <span class="text-[13px] text-[#1c1c1e] font-medium">{{ title }}</span>
      <Icon v-if="showArrow" name="arrow-right" />
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped></style>
