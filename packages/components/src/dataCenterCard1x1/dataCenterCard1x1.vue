<script setup lang="ts">
import DmpFlexCard from "@/flexCard/flexCard.vue";
import { computed, toRefs } from "vue";
import {
  DmpDataCenterCard1x1Emits,
  DmpDataCenterCard1x1Props,
  ListItem,
} from "./props.ts";

defineOptions({ name: "DmpDataCenterCard1x1" });
const props = defineProps<DmpDataCenterCard1x1Props>();
const emit = defineEmits<DmpDataCenterCard1x1Emits>();

const {
  updateTime,
  list,
  title,
  showArrow,
  countClass = [],
  unitClass = [],
} = toRefs(props);

const gridSpan = computed(() => {
  return { row: 1, col: list.value.length > 2 ? 2 : 1 };
});

const colStyle = computed(() => {
  return {
    "grid-template-columns": `repeat(${list.value.length > 2 ? list.value.length : 1}, minmax(25%, 1fr))`,
  };
});

const itemClass = computed(() => {
  if (list.value.length > 2) {
    return ["flex-col-reverse", "gap-[4px]", "items-center", "grid-item"];
  }
  return ["flex-col", "gap-[2px]"];
});

const fontSizeMap = {
  1: 24,
  2: 18,
};
const countClassComputed = computed(() => {
  const size = fontSizeMap?.[list.value.length] ?? 16;
  return [`text-[${size}px]`, `leading-[18px]`, ...countClass.value];
});

const showIcon = computed(() => {
  return list.value.length > 2;
});

const showUpdateTime = computed(() => {
  return list.value.length !== 2;
});

const updateTimeClass = computed(() => {
  return list.value.length > 2 ? "px-[8px]" : "px-[5px]";
});

const showPagination = computed(() => {
  return list.value.length > 4;
});

const handleItemClick = (item: ListItem) => {
  emit("click", item);
};
</script>

<template>
  <DmpFlexCard
    :title="title"
    class="h-[152px]"
    :grid-span="gridSpan"
    :show-arrow="showArrow"
  >
    <div class="flex flex-col h-full">
      <div class="w-full overflow-x-auto">
        <div class="grid" :style="colStyle">
          <template v-for="item in list" :key="item.lob">
            <div
              class="flex relative p-[8px] rounded-[12px] cursor-pointer hover:bg-[rgba(255,255,255,0.64)] hover:shadow-[0_2px_4px_0_rgba(0,0,0,0.04)] duration-150 ease-linear"
              :class="itemClass"
              @click="handleItemClick(item)"
            >
              <div class="text-[12px] leading-[16px] text-[rgba(28,28,30,.64)]">
                {{ item.lob }}
              </div>
              <div class="flex gap-[2px] items-baseline text-[#1C1C1E]">
                <span class="font-bold" :class="countClassComputed">
                  {{ item.count }}
                </span>
                <span class="text-[10px] leading-[12px]" :class="unitClass">
                  {{ item.unit }}
                </span>
              </div>
              <div v-if="showIcon" class="w-[20px] h-[20px] bg-black mb-[4px]">
                {{ item.icon }}
              </div>
            </div>
          </template>
        </div>
      </div>

      <div
        v-if="showUpdateTime"
        class="flex justify-between mt-auto py-[4px]"
        :class="updateTimeClass"
      >
        <div
          v-if="updateTime"
          class="text-[11px] leading-[16px] text-[rgba(28,28,30,.4)]"
        >
          {{ updateTime }} 更新
        </div>
        <div
          v-if="showPagination"
          class="w-[48px] h-[16px] rounded-[8px] bg-[rgba(28,28,30,.07)]"
        ></div>
      </div>
    </div>
  </DmpFlexCard>
</template>

<style lang="scss" scoped>
// :deep(.dmp-flex-card) {
//   padding-inline: 8px;
//   padding-bottom: 8px;

//   .title {
//     padding-inline: 8px;
//   }

//   .content {
//     flex: 1;

//     ::-webkit-scrollbar {
//       display: none;
//     }
//   }
// }

// .grid-item + .grid-item {
//   &::before {
//     content: "";
//     position: absolute;
//     top: 50%;
//     left: 0;
//     transform: translateY(-50%);
//     width: 1px;
//     height: 48px;
//     background: rgba(0, 0, 0, 0.04);
//   }
// }
</style>
