<script setup lang="ts">
import DmpFlexCard from "@/flexCard/flexCard.vue";
import DmpValueWithUnit from "@/valueWithUnit/ValueWithUnit.vue";
import { computed, ref, toRefs } from "vue";
import { DmpDataCenterCard1x1Emits, DmpDataCenterCard1x1Props, ListItem } from "./props.ts";

defineOptions({ name: "DmpDataCenterCard1x1" });
const emit = defineEmits<DmpDataCenterCard1x1Emits>();
const props = defineProps<DmpDataCenterCard1x1Props>();
const { updateTime, list, title, showArrow = ref(false), countClass, unitClass } = toRefs(props);

const gridSpan = computed(() => {
  return { row: 1, col: list.value.length > 2 ? 2 : 1 };
});

const colStyle = computed(() => {
  return {
    "grid-template-columns": `repeat(${list.value.length > 2 ? list.value.length : 1}, minmax(25%, 1fr))`,
  };
});

const itemClass = computed(() => {
  if (list.value.length > 2) {
    return ["flex-col-reverse", "gap-[4px]", "items-center", "grid-item"];
  }
  return ["flex-col", "gap-[2px]"];
});

// TODO 动态赋值会导致line-height失效，先写死
const selfCountClass = computed(() => {
  let className: string[];
  switch (list.value.length) {
    case 1:
      className = [`text-[24px], leading-[24px]`];
      break;
    case 2:
      className = [`text-[18px], leading-[18px]`];
      break;
    default:
      className = [`text-[16px], leading-[16px]`];
      break;
  }
  return [...className, ...countClass.value];
});

const showIcon = computed(() => {
  return list.value.length > 2;
});

const showUpdateTime = computed(() => {
  return list.value.length !== 2;
});

const updateTimeClass = computed(() => {
  return list.value.length > 2 ? "px-[8px]" : "px-[5px]";
});

const showPagination = computed(() => {
  return list.value.length > 4;
});

const handleItemClick = (item: ListItem) => {
  emit("click", item);
};
</script>

<template>
  <DmpFlexCard class="h-[152px]" :title="title" :grid-span="gridSpan" :show-arrow="showArrow">
    <div class="flex flex-col h-full">
      <div class="w-full overflow-x-auto">
        <div class="grid" :style="colStyle">
          <template v-for="item in list" :key="item.lob">
            <div
              class="flex relative p-[8px] rounded-[12px] cursor-pointer hover:bg-[rgba(255,255,255,0.64)] hover:shadow-[0_2px_4px_0_rgba(0,0,0,0.04)] duration-150 ease-linear"
              :class="itemClass"
              @click="handleItemClick(item)"
            >
              <div class="text-[12px] leading-[16px] text-[rgba(28,28,30,.64)]">
                {{ item.lob }}
              </div>
              <DmpValueWithUnit
                :value="item.count"
                :unit="item.unit"
                :value-class="selfCountClass"
                :unit-class="unitClass"
              />
              <div v-if="showIcon" class="w-[20px] h-[20px] bg-black mb-[4px]">
                {{ item.icon }}
              </div>
            </div>
          </template>
        </div>
      </div>

      <div
        v-if="showUpdateTime"
        class="flex justify-between mt-auto py-[4px]"
        :class="updateTimeClass"
      >
        <div v-if="updateTime" class="text-[11px] leading-[16px] text-[rgba(28,28,30,.4)]">
          {{ updateTime }} 更新
        </div>
        <div
          v-if="showPagination"
          class="w-[48px] h-[16px] rounded-[8px] bg-[rgba(28,28,30,.07)]"
        ></div>
      </div>
    </div>
  </DmpFlexCard>
</template>
