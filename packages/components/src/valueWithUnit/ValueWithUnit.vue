<script setup lang="ts">
import { toRefs } from "vue";
import { DmpValueWithUnitProps } from "./props.ts";

defineOptions({ name: "DmpValueWithUnit" });
const props = defineProps<DmpValueWithUnitProps>();
const { value, unit, valueClass, unitClass } = toRefs(props);
</script>

<template>
  <div class="flex items-end gap-[2px] text-[#1C1C1E]">
    <div class="text-[16px] font-medium leading-[16px]" :class="valueClass">
      {{ value }}
    </div>
    <div class="text-[10px] font-medium leading-[12px]" :class="unitClass">
      {{ unit }}
    </div>
  </div>
</template>
