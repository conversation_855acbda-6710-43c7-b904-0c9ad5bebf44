<script setup lang="ts">
import { ref, onMounted, nextTick, onUnmounted } from "vue";
import { useEventListener } from "@vueuse/core";
import { AutoScrollerProps } from "@/autoScroller/props.ts";

defineOptions({ name: "DmpAutoScroller" });

const {
  horizontal,
  disabled,
  width = "auto",
  height = "auto",
} = defineProps<AutoScrollerProps>();

const contentRef = ref(null);
const isScrollable = ref(false);
const isAtStart = ref(true);
const isAtEnd = ref(false);

function updateScrollState() {
  const el = contentRef.value;
  if (!el) return;
  isScrollable.value = horizontal
    ? el.scrollWidth > el.clientWidth
    : el.scrollHeight > el.clientHeight;
  isAtStart.value = horizontal ? el.scrollLeft <= 0 : el.scrollTop <= 0;
  // 允许 1px 误差,避免浮点
  isAtEnd.value = horizontal
    ? el.scrollLeft + el.clientWidth >= el.scrollWidth - 1
    : el.scrollTop + el.clientHeight >= el.scrollHeight - 1;
}

// 滚动到指定位置
function scrollByPage(pageOffset: number) {
  const el = contentRef.value;
  if (!el) return;
  const distance = (horizontal ? el.clientWidth : el.clientHeight) * pageOffset;
  if (horizontal) {
    el.scrollBy({ left: distance, behavior: "smooth" });
  } else {
    el.scrollBy({ top: distance, behavior: "smooth" });
  }
}

// useElementSize(contentRef, updateScrollState);

let stopResizeListener, stopScrollListener;

onMounted(() => {
  nextTick(() => {
    updateScrollState();
    stopResizeListener = useEventListener(window, "resize", updateScrollState);
    stopScrollListener = useEventListener(
      contentRef,
      "scroll",
      updateScrollState,
    );
  });
});

onUnmounted(() => {
  if (stopResizeListener) stopResizeListener();
  if (stopScrollListener) stopScrollListener();
});

defineExpose({ scrollByPage });
</script>

<template>
  <div class="dmp-auto-scroller grow">
    <slot
      name="indicator"
      :at-start="isAtStart"
      :at-end="isAtEnd"
      :scrollable="isScrollable"
    />
    <div
      ref="contentRef"
      class="content h-full overflow-hidden"
      :class="[disabled ? '' : (horizontal ? 'overflow-x-auto' : 'overflow-y-auto')]"
      :style="{ scrollbarWidth: 'none', width, height }"
    >
      <slot />
    </div>
  </div>
</template>
