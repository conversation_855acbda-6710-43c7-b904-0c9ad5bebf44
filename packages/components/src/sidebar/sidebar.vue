<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from "vue";
import { SidebarProps, SidebarEmits, MenuItem } from "./props.ts";
import { Icon } from "../icon/icon.ts";
import { dfs } from "@/utils";

defineOptions({ name: "DmpSidebar" });
const {
  menuItems = [],
  initPath,
  show,
  isMobile,
  footer,
} = defineProps<SidebarProps>();
const emit = defineEmits<SidebarEmits>();

// 当前激活的子菜单路径
const activeSubmenuPath = ref<string>("");

const initExpandedMenus = new Set<string>();
menuItems.forEach((menuItem) =>
  dfs<MenuItem>(
    menuItem,
    (item) => item.expanded && initExpandedMenus.add(item.id),
  ),
);
// 菜单项展开状态，使用菜单项ID作为键
const expandedMenus = ref<Set<string>>(initExpandedMenus);

// 检查是否是当前激活的父菜单
function isParentActive(item: MenuItem) {
  if (!item.children) return false;
  return item.children.some((child) => child.path === activeSubmenuPath.value);
}

// 检查是否是当前激活的子菜单
function isSubmenuActive(submenu: MenuItem) {
  return submenu.path === activeSubmenuPath.value;
}

// 切换菜单项展开状态
function toggleMenuItem(item: MenuItem) {
  console.log("toggleMenuItem", item);
  if (item.children) {
    // 有子菜单：只切换展开状态
    if (expandedMenus.value.has(item.id)) {
      expandedMenus.value.delete(item.id);
    } else {
      expandedMenus.value.add(item.id);
    }
  } else {
    // 没有子菜单：直接跳转并设置为激活状态
    activeSubmenuPath.value = item.path;
    emit("select", item);
  }
}

// 导航到子菜单
function navigateToSubmenu(submenu: MenuItem) {
  // 设置当前激活的子菜单
  activeSubmenuPath.value = submenu.path;

  emit("select", submenu);
}

// 监听路由变化，自动设置激活状态和展开父菜单
watch(
  () => initPath,
  (newPath) => {
    // 查找匹配的菜单项
    for (const item of menuItems) {
      if (item.children) {
        // 检查子菜单
        const matchedChild = item.children.find(
          (child) => child.path === newPath,
        );
        if (matchedChild) {
          activeSubmenuPath.value = newPath;
          // 只在父菜单当前未展开时才自动展开，避免强制展开已折叠的菜单
          if (!expandedMenus.value.has(item.id)) {
            expandedMenus.value.add(item.id);
          }
          return;
        }
      } else {
        // 没有子菜单的直接匹配
        if (item.path === newPath) {
          activeSubmenuPath.value = newPath;
          return;
        }
        // 特殊处理：根路径 "/" 也激活运营中心
        if (newPath === "/" && item.id === "operations") {
          activeSubmenuPath.value = item.path;
          // 只在父菜单当前未展开时才自动展开
          if (!expandedMenus.value.has(item.id)) {
            expandedMenus.value.add(item.id);
          }
          return;
        }
      }
    }
  },
  { immediate: true },
);
</script>

<template>
  <aside
    :class="[
      'dmp-sidebar fixed left-0 top-0 w-[200px] h-full flex flex-col transition-transform duration-300',
      // 移动端：使用更高的z-index以显示在遮罩层之上
      // 桌面端：使用较低的z-index
      isMobile ? 'z-40' : 'z-40',
      show ? 'translate-x-0' : '-translate-x-full',
    ]"
  >
    <!-- 侧边栏头部 -->
    <div class="p-[16px] flex-shrink-0">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-[3px]">
          <span
            class="font-semibold text-[#1c1c1e] text-[20px] leading-[32px] pl-[16px]"
            >MyBusiness</span
          >
        </div>
      </div>
    </div>

    <!-- 菜单列表 -->
    <nav class="flex-1 overflow-y-auto py-4">
      <ul class="space-y-[2px] px-[16px]">
        <li v-for="item in menuItems" :key="item.id">
          <!-- 主菜单项 -->
          <button
            @click="toggleMenuItem(item)"
            :class="[
              'w-full flex items-center justify-between px-[16px] rounded-[20px] h-[40px] text-left transition-colors group text-[13px]',
              // 父菜单高亮逻辑：有子菜单时看子菜单是否激活，没有子菜单时看自己是否激活
              (
                item.children
                  ? isParentActive(item)
                  : activeSubmenuPath === item.path
              )
                ? 'bg-[rgba(0,113,227,0.12)] text-[#0071E3]'
                : 'text-[#1C1C1E] hover:text-[#0071E3]',
            ]"
          >
            <div class="flex items-center gap-[4px]">
              <!-- SVG 图标 -->
              <Icon
                :v-if="item.icon"
                :name="item.icon"
                class="w-[16px] h-[16px] transition-colors"
                :class="
                  (
                    item.children
                      ? isParentActive(item)
                      : activeSubmenuPath === item.path
                  )
                    ? 'text-[#0071E3]'
                    : 'text-[#1C1C1E] group-hover:text-[#0071E3]'
                "
              />

              <span class="font-medium">{{ item.title }}</span>
            </div>

            <!-- 展开箭头 -->
            <svg
              v-if="item.children"
              :class="[
                'w-4 h-4 transition-all',
                expandedMenus.has(item.id) ? 'rotate-90' : '',
              ]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>

          <!-- 子菜单 -->
          <ul
            v-if="item.children && expandedMenus.has(item.id)"
            class="mt-[2px] space-y-[1px]"
          >
            <li v-for="submenu in item.children" :key="submenu.title">
              <button
                @click="navigateToSubmenu(submenu)"
                :class="[
                  'w-full text-left px-[16px] h-[32px] rounded-[16px] text-sm transition-colors pl-[36px]',
                  isSubmenuActive(submenu)
                    ? 'bg-[rgba(0,113,227,0.08)] text-[#0071E3] font-medium'
                    : 'text-[#6E6E73] hover:text-[#0071E3] hover:bg-[rgba(0,113,227,0.08)]',
                ]"
              >
                {{ submenu.title }}
              </button>
            </li>
          </ul>
        </li>
      </ul>
    </nav>

    <!-- 侧边栏底部 -->
    <div class="p-4 border-gray-100 flex-shrink-0">
      <div class="text-xs text-gray-500 text-center">
        {{ footer || "© 2024 MyBusiness" }}
      </div>
    </div>
  </aside>
</template>
