function DmpResolver() {
  return {
    type: "component",
    resolve: (name) => {
      if (name.startsWith("Dmp")) {
        console.log(`${name} resolved`);
        const partialName = name.slice(3).toLowerCase();
        return {
          name,
          from: `@dmp/components`,
          sideEffects: `@dmp/components/dist/es/${partialName}/index.css`,
        };
      }
    },
  };
}

module.exports = {
  DmpResolver,
};
